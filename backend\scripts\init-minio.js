import { initializeBucket, testMinioConnection } from '../lib/minio.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

async function initMinio() {
  console.log('🔄 开始初始化MinIO...');
  
  try {
    // 测试连接
    console.log('📡 测试MinIO连接...');
    const connected = await testMinioConnection();
    
    if (!connected) {
      console.error('❌ MinIO连接失败，请检查：');
      console.error('   1. MinIO服务是否已启动');
      console.error('   2. 端口配置是否正确');
      console.error('   3. 访问密钥是否正确');
      process.exit(1);
    }
    
    // 初始化存储桶
    console.log('🪣 初始化存储桶...');
    await initializeBucket();
    
    console.log('✅ MinIO初始化完成！');
    console.log(`📁 存储桶名称: ${process.env.MINIO_BUCKET || 'digital-hometown'}`);
    console.log(`🌐 访问地址: ${process.env.MINIO_USE_SSL === 'true' ? 'https' : 'http'}://${process.env.MINIO_ENDPOINT || 'localhost'}:${process.env.MINIO_PORT || 9000}`);
    console.log('🔓 存储桶已设置为公有读取模式');
    
  } catch (error) {
    console.error('❌ MinIO初始化失败:', error.message);
    console.error('请检查MinIO服务状态和配置');
    process.exit(1);
  }
}

// 运行初始化
initMinio();
